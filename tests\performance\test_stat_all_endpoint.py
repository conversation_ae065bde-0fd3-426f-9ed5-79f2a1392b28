#!/usr/bin/env python3
"""
Performance test script for the optimized /basic/stat/all endpoint.
Tests various parameter combinations to verify performance improvements.
"""

import asyncio
import aiohttp
import time
import sys
import os

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from server.base.config import get_logger

logger = get_logger()

BASE_URL = "http://localhost:9022"

async def test_endpoint_performance():
    """Test the /basic/stat/all endpoint with various parameters"""
    
    test_cases = [
        {
            "name": "Recent 7 days",
            "params": {"vtuber": "星瞳", "recent": 7},
            "expected_max_time": 2.0
        },
        {
            "name": "Recent 30 days", 
            "params": {"vtuber": "星瞳", "recent": 30},
            "expected_max_time": 3.0
        },
        {
            "name": "Recent 90 days",
            "params": {"vtuber": "星瞳", "recent": 90}, 
            "expected_max_time": 5.0
        },
        {
            "name": "All data (default limit 1000)",
            "params": {"vtuber": "星瞳", "recent": -1},
            "expected_max_time": 8.0
        },
        {
            "name": "All data with custom limit 500",
            "params": {"vtuber": "星瞳", "recent": -1, "limit": 500},
            "expected_max_time": 6.0
        },
        {
            "name": "All data with custom limit 100",
            "params": {"vtuber": "星瞳", "recent": -1, "limit": 100},
            "expected_max_time": 3.0
        }
    ]
    
    async with aiohttp.ClientSession() as session:
        logger.info("Starting performance tests for /basic/stat/all endpoint")
        logger.info("=" * 60)
        
        for i, test_case in enumerate(test_cases, 1):
            logger.info(f"Test {i}/{len(test_cases)}: {test_case['name']}")
            
            start_time = time.time()
            
            try:
                async with session.get(
                    f"{BASE_URL}/basic/stat/all",
                    params=test_case["params"],
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    
                    end_time = time.time()
                    response_time = end_time - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        
                        # Extract performance metrics
                        total_records = data.get("data", {}).get("total_records", 0)
                        query_time = data.get("data", {}).get("query_time_seconds", 0)
                        limit_applied = data.get("data", {}).get("limit_applied")
                        
                        logger.info(f"  ✓ Status: {response.status}")
                        logger.info(f"  ✓ Response time: {response_time:.3f}s")
                        logger.info(f"  ✓ Query time: {query_time:.3f}s")
                        logger.info(f"  ✓ Records returned: {total_records}")
                        if limit_applied:
                            logger.info(f"  ✓ Limit applied: {limit_applied}")
                        
                        # Check if performance meets expectations
                        if response_time <= test_case["expected_max_time"]:
                            logger.info(f"  ✓ Performance: PASS (under {test_case['expected_max_time']}s)")
                        else:
                            logger.warning(f"  ⚠ Performance: SLOW (over {test_case['expected_max_time']}s)")
                            
                    else:
                        logger.error(f"  ✗ HTTP Error: {response.status}")
                        error_data = await response.text()
                        logger.error(f"  ✗ Error details: {error_data}")
                        
            except asyncio.TimeoutError:
                logger.error(f"  ✗ Timeout: Request took longer than 30 seconds")
            except Exception as e:
                logger.error(f"  ✗ Error: {e}")
                
            logger.info("-" * 40)
            
            # Wait between tests to avoid overwhelming the server
            await asyncio.sleep(1)
        
        logger.info("Performance tests completed")

async def test_error_cases():
    """Test error handling"""
    
    error_test_cases = [
        {
            "name": "Invalid vtuber",
            "params": {"vtuber": "nonexistent", "recent": 7},
            "expected_status": 404
        },
        {
            "name": "Invalid recent parameter",
            "params": {"vtuber": "星瞳", "recent": -2},
            "expected_status": 400
        },
        {
            "name": "Invalid limit parameter",
            "params": {"vtuber": "星瞳", "recent": 7, "limit": -1},
            "expected_status": 400
        }
    ]
    
    async with aiohttp.ClientSession() as session:
        logger.info("\nTesting error handling")
        logger.info("=" * 40)
        
        for i, test_case in enumerate(error_test_cases, 1):
            logger.info(f"Error Test {i}/{len(error_test_cases)}: {test_case['name']}")
            
            try:
                async with session.get(
                    f"{BASE_URL}/basic/stat/all",
                    params=test_case["params"],
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    
                    if response.status == test_case["expected_status"]:
                        logger.info(f"  ✓ Expected error status: {response.status}")
                    else:
                        logger.error(f"  ✗ Unexpected status: {response.status} (expected {test_case['expected_status']})")
                        
            except Exception as e:
                logger.error(f"  ✗ Error: {e}")
                
            logger.info("-" * 20)

async def main():
    """Run all tests"""
    try:
        await test_endpoint_performance()
        await test_error_cases()
        logger.info("\n🎉 All tests completed!")
    except Exception as e:
        logger.error(f"Test suite failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
